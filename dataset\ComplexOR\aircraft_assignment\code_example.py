def aircraft_assignment(availability, demand, capabilities, costs):
    """
    Args:
        availability: list, availability of each aircraft
        demand: list, demand for each route
        capabilities: 2D list, capabilities of each aircraft for each route
        costs: 2D list, costs of assigning each aircraft to each route

    Returns:
        min_total_cost: float, the minimum total cost of the assignment
    """
    min_total_cost = 0  # Placeholder for the result of the optimization
    return min_total_cost