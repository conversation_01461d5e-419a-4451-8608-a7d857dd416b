def nltrans(supply, demand, rate, limit):
    """
    Args:
        supply: a list of integers, each indicates the amount of goods available at an origin
        demand: a list of integers, each indicates the amount of goods required at a destination
        rate: a 2D list of integers, the shipment costs per unit from each origin to each destination
        limit: a 2D list of integers, the limit on units shipped from each origin to each destination

    Returns:
        total_cost: an integer, denotes the minimum total cost of shipping goods
    """
    # To be implemented
    total_cost = 0
    return total_cost